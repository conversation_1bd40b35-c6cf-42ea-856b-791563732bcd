package nccloud.api.test.utils;

import java.io.*;
import java.net.*;
import java.nio.charset.Charset;
import java.util.*;

/**
 * 华电API调用主程序
 *
 * <AUTHOR>
 */
public class Main {

    public static void main(String[] args) {
        try {
            System.out.println("华电API工具启动...");

            // 测试加密解密功能
            testEncryption();

            // 测试获取token（需要配置实际参数）
            // String token = getTokenByClient();
            // System.out.println("获取到的token: " + token);

        } catch (Exception e) {
            System.err.println("程序执行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试加密解密功能
     */
    private static void testEncryption() throws Exception {
        System.out.println("\n=== 测试加密解密功能 ===");

        // 生成RSA密钥对
        KeyPairs keyPairs = KeysFactory.buildAsymKey();
        String publicKey = keyPairs.getPublicKey();
        String privateKey = keyPairs.getPrivateKey();

        System.out.println("公钥: " + publicKey.substring(0, 50) + "...");
        System.out.println("私钥: " + privateKey.substring(0, 50) + "...");

        // 测试RSA加密解密
        String testData = "Hello, 华电系统!";
        System.out.println("原始数据: " + testData);

        String encrypted = Encryption.pubEncrypt(publicKey, testData);
        System.out.println("RSA加密后: " + encrypted.substring(0, 50) + "...");

        String decrypted = Decryption.priDecrypt(privateKey, encrypted);
        System.out.println("RSA解密后: " + decrypted);

        // 测试SHA256
        String hash = SHA256Util.getSHA256(testData);
        System.out.println("SHA256哈希: " + hash);

        // 测试AES对称加密
        String symKey = KeysFactory.buildSymKey();
        System.out.println("AES密钥: " + symKey.substring(0, 20) + "...");

        String aesEncrypted = Encryption.symEncrypt(symKey, testData);
        System.out.println("AES加密后: " + aesEncrypted);

        String aesDecrypted = Decryption.symDecrypt(symKey, aesEncrypted);
        System.out.println("AES解密后: " + aesDecrypted);
    }

    /**
     * 获取客户端token
     */
    private static String getTokenByClient() throws Exception {
        Map<String, String> paramMap = new HashMap<String, String>();

        // TODO: 这些参数需要根据实际环境配置
        String biz_center = "NCC";
        String username = "lijun";
        String client_secret = "your_client_secret_here";
        String pubKey = "your_public_key_here";
        String client_id = "MDM";

        // 密码模式认证
        paramMap.put("grant_type", "client_credentials");
        // 第三方应用id
        paramMap.put("client_id", client_id);
        // 第三方应用secret 公钥加密
        paramMap.put("client_secret", URLEncoder.encode(Encryption.pubEncrypt(pubKey, client_secret), "utf-8"));
        // 账套编码
        paramMap.put("biz_center", biz_center);
        // 传递数据源和ncc登录用户
        paramMap.put("dsname", "NCC");
        paramMap.put("usercode", username);
        // 签名
        String sign = SHA256Util.getSHA256(client_id + client_secret + pubKey);
        paramMap.put("signature", sign);

        String url = "http://your_server_ip:port/nccloud/opm/accesstoken";
        String mediaType = "application/x-www-form-urlencoded";
        String token = doPost(url, paramMap, mediaType, null, "");
        return token;
    }

    /**
     * 执行POST请求
     */
    private static String doPost(String baseUrl, Map<String, String> paramMap, String mediaType, Map<String, String> headers, String json) {
        HttpURLConnection urlConnection = null;
        InputStream in = null;
        OutputStream out = null;
        BufferedReader bufferedReader = null;
        String result = null;

        try {
            StringBuffer sb = new StringBuffer();
            sb.append(baseUrl);
            if (paramMap != null && !paramMap.isEmpty()) {
                sb.append("?");
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    sb.append(key + "=" + value).append("&");
                }
                baseUrl = sb.toString().substring(0, sb.toString().length() - 1);
            }

            URL urlObj = new URL(baseUrl);
            urlConnection = (HttpURLConnection) urlObj.openConnection();
            urlConnection.setConnectTimeout(50000);
            urlConnection.setReadTimeout(50000);
            urlConnection.setRequestMethod("POST");
            urlConnection.setDoOutput(true);
            urlConnection.setDoInput(true);
            urlConnection.setUseCaches(false);
            urlConnection.addRequestProperty("content-type", mediaType);

            if (headers != null) {
                for (String key : headers.keySet()) {
                    urlConnection.addRequestProperty(key, headers.get(key));
                }
            }

            out = urlConnection.getOutputStream();
            if (json != null && !json.isEmpty()) {
                out.write(json.getBytes("utf-8"));
            }
            out.flush();

            int resCode = urlConnection.getResponseCode();
            System.out.println("HTTP响应码: " + resCode);

            if (resCode == HttpURLConnection.HTTP_OK || resCode == HttpURLConnection.HTTP_CREATED || resCode == HttpURLConnection.HTTP_ACCEPTED) {
                in = urlConnection.getInputStream();
            } else {
                in = urlConnection.getErrorStream();
            }

            bufferedReader = new BufferedReader(new InputStreamReader(in, "utf-8"));
            StringBuffer temp = new StringBuffer();
            String line = bufferedReader.readLine();
            while (line != null) {
                temp.append(line).append("\r\n");
                line = bufferedReader.readLine();
            }

            String ecod = urlConnection.getContentEncoding();
            if (ecod == null) {
                ecod = Charset.forName("utf-8").name();
            }
            result = new String(temp.toString().getBytes("utf-8"), ecod);

        } catch (Exception e) {
            System.err.println("HTTP请求异常: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (null != bufferedReader) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != out) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (urlConnection != null) {
                urlConnection.disconnect();
            }
        }
        return result;
    }
}