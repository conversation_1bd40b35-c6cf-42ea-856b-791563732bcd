private static String getTokenByClient() throws Exception {
Map<String, String> paramMap = new HashMap<String, String>();
//
String biz_center = "";
String username = "";
String client_secret = "";
String pubKey = "";
String client_id = "MDM";
// 密码模式认证
paramMap.put("grant_type", "client_credentials");
// 第三方应用id
paramMap.put("client_id", client_id);
// 第三方应用secret 公钥加密
paramMap.put("client_secret", URLEncoder.encode(Encryption.pubEncrypt(pubKey, client_secret), "utf-8"));
// 账套编码
paramMap.put("biz_center", "NCC");
// // TODO 传递数据源和ncc登录用户"
paramMap.put("dsname", "NCC");
paramMap.put("usercode", "lijun");
// 签名
String sign = SHA256Util.getSHA256(client_id + client_secret + pubKey);
paramMap.put("signature", sign);
String url = "http://ip:port/nccloud/opm/accesstoken";
String mediaType = "application/x-www-form-urlencoded";
String token = doPost(url, paramMap, mediaType, null, "");
return token;
}

private static String doPost(String baseUrl, Map<String, String> paramMap, String mediaType, Map<String, String> headers, String json) {
HttpURLConnection urlConnection = null;
InputStream in = null;
OutputStream out = null;
BufferedReader bufferedReader = null;
String result = null;
try {
StringBuffer sb = new StringBuffer();
sb.append(baseUrl);
if (paramMap != null) {
sb.append("?");
for (Map.Entry<String, String> entry : paramMap.entrySet()) {
String key = entry.getKey();
String value = entry.getValue();
sb.append(key + "=" + value).append("&");
}
baseUrl = sb.toString().substring(0, sb.toString().length() - 1);
}
URL urlObj = new URL(baseUrl);
urlConnection = (HttpURLConnection) urlObj.openConnection();
urlConnection.setConnectTimeout(50000);
urlConnection.setRequestMethod("POST");
urlConnection.setDoOutput(true);
urlConnection.setDoInput(true);
urlConnection.setUseCaches(false);
urlConnection.addRequestProperty("content-type", mediaType);
if (headers != null) {
for (String key : headers.keySet()) {
urlConnection.addRequestProperty(key, headers.get(key));
}
}
out = urlConnection.getOutputStream();
out.write(json.getBytes("utf-8"));
out.flush();
int resCode = urlConnection.getResponseCode();
if (resCode == HttpURLConnection.HTTP_OK || resCode == HttpURLConnection.HTTP_CREATED || resCode == HttpURLConnection.HTTP_ACCEPTED) {
in = urlConnection.getInputStream();
} else {
in = urlConnection.getErrorStream();
}
bufferedReader = new BufferedReader(new InputStreamReader(in, "utf-8"));
StringBuffer temp = new StringBuffer();
String line = bufferedReader.readLine();
while (line != null) {
temp.append(line).append("\r\n");
line = bufferedReader.readLine();
}
String ecod = urlConnection.getContentEncoding();
if (ecod == null) {
ecod = Charset.forName("utf-8").name();
}
result = new String(temp.toString().getBytes("utf-8"), ecod);
} catch (Exception e) {
System.out.println(e);
} finally {
if (null != bufferedReader) {
try {
bufferedReader.close();
} catch (IOException e) {
e.printStackTrace();
}
}
if (null != out) {
try {
out.close();
} catch (IOException e) {
e.printStackTrace();
}
}
if (null != in) {
try {
in.close();
} catch (IOException e) {
e.printStackTrace();
}
}
urlConnection.disconnect();
}
return result;
}