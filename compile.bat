@echo off
echo ========================================
echo 华电工具类项目编译脚本
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查Java环境
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装JDK并配置PATH环境变量
    pause
    exit /b 1
)

javac -version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java编译器，请确保已安装JDK并配置PATH环境变量
    pause
    exit /b 1
)

echo Java环境检查通过

REM 创建输出目录
if not exist "classes" mkdir classes

echo 开始编译Java源文件...

REM 编译所有Java文件
javac -encoding UTF-8 -d classes -cp . src\nccloud\api\test\utils\*.java

if %errorlevel% equ 0 (
    echo 编译成功！
    echo 编译输出目录：classes
    echo ========================================
) else (
    echo 编译失败！请检查源代码错误
    pause
    exit /b 1
)

pause
