package nccloud.api.test.utils;

import java.util.Base64;

/**
 * Base64工具类
 * 
 * <AUTHOR>
 *
 */
public class Base64Util {
    
    /**
     * Base64编码
     */
	public static String encryptBASE64(byte[] key) {
        return Base64.getEncoder().encodeToString(key);
    }

    /**
     * Base64解码
     */
	public static byte[] decryptBASE64(String key) {
        return Base64.getDecoder().decode(key);
    }

}
