# 华电工具类项目设计文档

## 项目概述
这是一个Java工具类项目，主要用于华电系统的API调用，包含加密解密、HTTP请求等功能。

## 项目结构
```
utils/
├── docs/
│   └── design.md          # 设计文档
├── src/
│   └── nccloud/api/test/utils/  # 主要包路径
│       ├── Main.java      # 主程序入口
│       ├── Base64Util.java    # Base64编码解码工具
│       ├── CipherConstant.java # 加密算法常量
│       ├── CompressUtil.java   # 压缩工具
│       ├── Decryption.java     # 解密工具
│       ├── Encryption.java     # 加密工具
│       ├── KeyPairs.java       # 密钥对封装
│       ├── KeysFactory.java    # 密钥工厂
│       ├── ResultMessageUtil.java # 结果消息工具
│       └── SHA256Util.java     # SHA256哈希工具
├── compile.bat            # 编译脚本
├── run.bat               # 运行脚本
└── README.md             # 项目说明
```

## 核心功能模块

### 1. 加密解密模块
- **Encryption.java**: 提供对称加密(AES)和非对称加密(RSA)功能
- **Decryption.java**: 提供对称解密和非对称解密功能
- **KeysFactory.java**: 密钥生成和管理工厂类
- **KeyPairs.java**: RSA密钥对封装类
- **CipherConstant.java**: 加密算法常量定义

### 2. 工具类模块
- **Base64Util.java**: Base64编码解码工具
- **SHA256Util.java**: SHA256哈希计算工具
- **CompressUtil.java**: 数据压缩工具
- **ResultMessageUtil.java**: 结果消息处理工具

### 3. 主程序模块
- **Main.java**: 程序入口，包含HTTP客户端功能和token获取逻辑

## 技术特点

### 加密算法
- **对称加密**: AES-256
- **非对称加密**: RSA-1024
- **哈希算法**: SHA-256
- **编码方式**: Base64

### HTTP客户端
- 使用Java原生HttpURLConnection
- 支持POST请求
- 支持自定义请求头
- 支持URL编码参数

### 兼容性
- Java 8+兼容
- 使用标准Java API，避免依赖第三方库
- 替换废弃的sun.misc包为java.util.Base64

## 使用场景
主要用于华电NCC系统的API调用：
1. 客户端认证token获取
2. 数据加密传输
3. 签名验证
4. HTTP接口调用

## 编译和运行
- 使用compile.bat编译所有Java文件
- 使用run.bat运行主程序
- 支持Windows批处理脚本自动化

## 注意事项
1. 公钥、私钥、client_secret等敏感信息需要在实际使用时配置
2. 服务器地址和端口需要根据实际环境修改
3. 编码统一使用UTF-8
4. 异常处理需要根据业务需求完善
