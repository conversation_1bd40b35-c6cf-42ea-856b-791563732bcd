# 华电工具类项目

这是一个Java工具类项目，主要用于华电系统的API调用，包含加密解密、HTTP请求等功能。

## 项目结构

```
utils/
├── docs/
│   └── design.md          # 设计文档
├── src/
│   └── nccloud/api/test/utils/  # 主要包路径
│       ├── Main.java      # 主程序入口
│       ├── Base64Util.java    # Base64编码解码工具
│       ├── CipherConstant.java # 加密算法常量
│       ├── CompressUtil.java   # 压缩工具
│       ├── Decryption.java     # 解密工具
│       ├── Encryption.java     # 加密工具
│       ├── KeyPairs.java       # 密钥对封装
│       ├── KeysFactory.java    # 密钥工厂
│       ├── ResultMessageUtil.java # 结果消息工具
│       └── SHA256Util.java     # SHA256哈希工具
├── classes/               # 编译输出目录（运行后生成）
├── compile.bat           # 编译脚本
├── run.bat              # 运行脚本
└── README.md            # 项目说明
```

## 快速开始

### 环境要求
- Java 8 或更高版本
- Windows 操作系统（支持批处理脚本）

### 编译和运行

1. **编译项目**
   ```bash
   compile.bat
   ```
   
2. **运行程序**
   ```bash
   run.bat
   ```

### 手动编译和运行

如果批处理脚本无法使用，可以手动执行以下命令：

```bash
# 创建输出目录
mkdir classes

# 编译
javac -encoding UTF-8 -d classes -cp . src\nccloud\api\test\utils\*.java

# 运行
java -cp classes nccloud.api.test.utils.Main
```

## 功能特性

### 加密解密功能
- **RSA非对称加密**: 支持公钥加密、私钥解密
- **AES对称加密**: 支持AES-256加密解密
- **SHA256哈希**: 支持数据签名和验证
- **Base64编码**: 支持数据编码解码

### HTTP客户端
- 支持POST请求
- 支持自定义请求头
- 支持URL编码参数
- 支持超时设置

### 华电API集成
- 客户端认证token获取
- 数据加密传输
- 签名验证

## 使用示例

程序运行后会自动执行以下测试：

1. **生成RSA密钥对**
2. **测试RSA加密解密**
3. **测试AES对称加密解密**
4. **测试SHA256哈希计算**

## 配置说明

如需调用华电API，请在 `Main.java` 中的 `getTokenByClient()` 方法中配置以下参数：

- `client_secret`: 客户端密钥
- `pubKey`: 公钥
- `url`: API服务器地址

## 注意事项

1. 所有敏感信息（密钥、服务器地址等）需要根据实际环境配置
2. 编码统一使用UTF-8
3. 建议在生产环境中完善异常处理和日志记录
4. 密钥管理需要遵循安全最佳实践

## 技术栈

- Java 8+
- 标准Java加密API (JCE)
- HttpURLConnection
- Base64编码

## 更新日志

- v1.0: 初始版本，包含基础加密解密和HTTP客户端功能
- 修复了Base64工具类的兼容性问题（使用java.util.Base64替代sun.misc包）
- 完善了异常处理和日志输出
