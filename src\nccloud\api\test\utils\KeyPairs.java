package nccloud.api.test.utils;

import java.security.KeyPair;

/**
 * KeyPairs 主键对
 * 
 * <AUTHOR>
 * @date: 2019-5-20下午4:34:51
 *
 */
public class KeyPairs {

	private KeyPair keyPair;
	
	public KeyPairs(KeyPair keyPair){
		this.keyPair = keyPair;
	}
	
	public String getPublicKey(){
		return Base64Util.encryptBASE64(keyPair.getPublic().getEncoded());
	}
	
	public String getPrivateKey(){
		return Base64Util.encryptBASE64(keyPair.getPrivate().getEncoded());
	}
}
