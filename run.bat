@echo off
echo ========================================
echo 华电工具类项目运行脚本
echo ========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查是否已编译
if not exist "classes" (
    echo 错误：未找到编译输出目录，请先运行compile.bat进行编译
    pause
    exit /b 1
)

if not exist "classes\nccloud\api\test\utils\Main.class" (
    echo 错误：未找到Main.class文件，请先运行compile.bat进行编译
    pause
    exit /b 1
)

echo 开始运行华电工具类程序...
echo ========================================

REM 运行主程序
java -cp classes nccloud.api.test.utils.Main

echo ========================================
echo 程序运行完成

pause
